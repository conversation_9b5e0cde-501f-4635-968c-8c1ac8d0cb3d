{"name": "ai-chatroom", "version": "2.1.0", "homepage": "https://shen-xiaodan.github.io/ai-chatroom", "description": "A pure frontend AI chatroom application with modular architecture and local storage", "main": "src/index.html", "scripts": {"start": "npx http-server src -p 3000 -o", "dev": "npx http-server src -p 3000 -o", "build": "node build/build.js", "predeploy": "npm run build", "deploy": "gh-pages -d dist"}, "dependencies": {"marked": "^16.1.1"}, "devDependencies": {"fs-extra": "^11.3.0", "gh-pages": "^6.3.0", "http-server": "^14.1.1", "prettier": "3.6.2"}}