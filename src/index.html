<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Chatroom</title>
  <link rel="stylesheet" href="styles.css">
  <!-- Marked.js for Markdown rendering -->
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>
<body>
  <div class="chat-container">
    <!-- 对话主题侧边栏 -->
    <div class="sidebar" id="sidebar">
      <div class="sidebar-header">
        <h3>💬 历史对话</h3>
        <button class="new-session-btn" id="new-session-btn" title="新建对话">+</button>
        <button class="collapse-btn" id="collapse-btn">-</button>
      </div>
      <div class="session-list" id="session-list">
        <!-- 会话列表将在这里动态生成 -->
      </div>
    </div>

    <!-- 主聊天区域 -->
    <div class="main-chat">
      <div class="chat-header">
        <h1>🤖 AI Chatroom</h1>
        <div class="header-actions">
          <div class="ai-info">
            <span class="ai-name" id="ai-name">DeepSeek V3</span>
            <span class="config-status" id="config-status">未配置</span>
          </div>
          <button class="settings-btn" id="settings-btn" title="设置">⚙️</button>
        </div>
      </div>

      <div id="chat-messages" class="chat-messages">
        <div class="message ai-message">
          <div class="avatar">🤖</div>
          <div class="content">
            <div class="text" id="welcome-message">
              <!-- 欢迎消息将通过 JavaScript 渲染 -->
            </div>
            <div class="timestamp">Just now</div>
          </div>
        </div>
      </div>

      <div class="chat-input">
        <textarea id="user-input" placeholder="Type your message here..." rows="1"></textarea>
        <button id="send-btn">Send</button>
      </div>

      <div class="footer">
        <p>AI Chatroom |
          <span style="color: var(--help-text-color); font-size: 0.9em;">数据仅存储在本地浏览器</span>
        </p>
      </div>
    </div>
  </div>

  <!-- 配置模态框 -->
  <div class="modal" id="config-modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>⚙️ API 配置</h2>
        <button class="close-btn" id="close-config-modal">&times;</button>
      </div>

      <div class="modal-body">
        <div class="config-form">
          <div class="form-group">
            <label for="api-provider">API 提供商:</label>
            <select id="api-provider">
              <option value="siliconflow">SiliconFlow</option>
              <option value="openai">OpenAI</option>
              <option value="custom">自定义</option>
            </select>
          </div>

          <div class="form-group">
            <label for="api-key">API Key: <span class="required">*</span></label>
            <input type="password" id="api-key" placeholder="请输入您的 API Key（仅存储在本地浏览器）" required>
            <button type="button" class="toggle-password" id="toggle-api-key">👁️</button>
            <small class="form-help">您的 API Key 将安全地存储在浏览器本地，不会上传到任何服务器</small>
          </div>

          <div class="form-group">
            <label for="base-url">Base URL: <span class="required">*</span></label>
            <input type="url" id="base-url" placeholder="https://api.example.com/v1" required>
          </div>

          <div class="form-group">
            <label for="model-name">模型名称: <span class="required">*</span></label>
            <select id="model-name">
              <option value="">请选择模型</option>
            </select>
            <input type="text" id="custom-model" placeholder="或输入自定义模型名称" style="display: none;">
          </div>

          <div class="form-group">
            <label for="max-tokens">最大 Token 数:</label>
            <input type="number" id="max-tokens" min="1" max="8192" value="2048">
          </div>

          <div class="form-group">
            <label for="temperature">温度值 (0-2):</label>
            <input type="number" id="temperature" min="0" max="2" step="0.1" value="0.7">
          </div>

          <div class="config-status-message" id="config-status-message"></div>

          <div class="form-actions">
            <button type="button" class="btn btn-test" id="test-config">测试连接</button>
            <button type="button" class="btn btn-reset" id="reset-config">重置配置</button>
            <button type="button" class="btn btn-primary" id="save-config">保存配置</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 首次配置提示模态框 -->
  <div class="modal" id="welcome-config-modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>🎉 欢迎使用 AI Chatroom</h2>
      </div>

      <div class="modal-body">
        <div class="welcome-message">
          <p>欢迎使用 AI Chatroom！这是一个纯前端的 AI 聊天应用，您的 API Key 将安全地存储在本地浏览器中。</p>
          <p>在开始聊天之前，请先配置您的 API 信息：</p>
          <ul>
            <li><strong>API Key</strong>（必填）- 从您的 AI 服务提供商获取</li>
            <li><strong>API Base URL</strong>（已预设）- 支持 OpenAI 兼容的 API</li>
            <li><strong>模型名称</strong>（可选择）- 从预设列表中选择或自定义</li>
          </ul>
          <p><strong>隐私保护：</strong>所有配置信息仅存储在您的浏览器本地，不会上传到任何服务器。</p>
        </div>

        <div class="form-actions">
          <button type="button" class="btn btn-primary" id="start-config">开始配置</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 核心管理器 -->
  <script src="session-manager.js"></script>
  <script src="config-manager.js"></script>

  <!-- 功能模块 -->
  <script src="ui-utils.js"></script>
  <script src="state-manager.js"></script>
  <script src="api-client.js"></script>
  <script src="message-manager.js"></script>
  <script src="config-ui.js"></script>

  <!-- 主脚本 -->
  <script src="script.js"></script>
</body>
</html>