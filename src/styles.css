:root {
  --primary-color: #a48ccd;
  --secondary-color: #5661ac;
  --ai-color: #7dabf6bc;
  --user-color: #f7809cdd;
  --bg-color: #cbcaf7;
  --card-color: #c3cafb;
  --text-color: #333333;
  --light-text: #6c757d;
  --border-color: #d9c9f3;
  --success-color: #f158a7;
  --help-text-color: #6c757d;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background: linear-gradient(135deg, #f0eafa 0%, #b99dd1 100%);
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.chat-container {
  width: 100%;
  max-width: 1200px;
  height: 95vh;
  background-color: var(--card-color);
  border-radius: 40px;
  box-shadow: 0 10px 30px rgba(72, 51, 57, 0.401);
  display: flex;
  flex-direction: row;
  overflow: hidden;
}

/* 侧边栏样式 */
.sidebar {
  width: 300px;
  background-color: #f8f9fa;
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: width 0.3s ease;
}

/* 侧边栏折叠状态 */
.sidebar.collapsed {
  width: 56px; /* 保留一个把手宽度，便于再次展开 */
}

.sidebar.collapsed .sidebar-header {
  justify-content: center;
  padding: 15px 10px;
}

.sidebar.collapsed .sidebar-header h3,
.sidebar.collapsed .new-session-btn {
  display: none;
}

.sidebar.collapsed .session-list {
  display: none;
}

.sidebar-header {
  padding: 20px 15px 15px;
  border-bottom: 1px solid var(--border-color);
  height: 70px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  background: var(--primary-color);
  color: white;
}

/* 右侧按钮对齐 */
.sidebar-header .new-session-btn {
  margin-left: auto;
}
.sidebar-header .collapse-btn {
  margin-left: 8px;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.new-session-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  font-size: 1.2rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.new-session-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.collapse-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  font-size: 1.2rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.collapse-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.session-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
}

.session-item {
  padding: 12px 15px;
  margin: 2px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  animation: slideInLeft 0.3s ease-out;
}

.session-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.session-item.active {
  background-color: rgba(247, 129, 161, 0.1);
  border-left-color: var(--primary-color);
}

.session-title {
  font-weight: 500;
  margin-bottom: 4px;
  font-size: 0.9rem;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.session-meta {
  font-size: 0.75rem;
  color: var(--light-text);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.session-actions {
  display: none;
  gap: 5px;
}

.session-item:hover .session-actions {
  display: flex;
}

.session-action-btn {
  background: none;
  border: none;
  color: var(--light-text);
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 0.7rem;
}

.session-action-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--text-color);
}

/* 主聊天区域 */
.main-chat {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.main-chat .chat-header {
  background: var(--primary-color);
  border-bottom: 1px solid var(--border-color);
  color: white;
  padding: 15px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 40px;
  position: relative;
  height: 70px;
}

.main-chat .chat-header h1 {
  font-size: 1.8rem;
  margin: 0;
}

.ai-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.ai-name {
  font-weight: 600;
  font-size: 1.1rem;
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
  background-color: #f9fafb;
}

.message {
  display: flex;
  max-width: 80%;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.ai-message {
  align-self: flex-start;
}

.user-message {
  align-self: flex-end;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
  margin-right: 10px;
  flex-shrink: 0;
}

.ai-message .avatar {
  background-color: var(--ai-color);
  color: white;
}

.user-message .avatar {
  background-color: var(--user-color);
  color: white;
}

.message .content {
  display: flex;
  flex-direction: column;
  margin-right: 10px;
  position: relative;
}

.ai-message .content .text {
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: 20px 20px 20px 0;
  padding: 12px 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.user-message .content .text {
  background-color: var(--primary-color);
  color: white;
  border-radius: 20px 20px 0 20px;
  padding: 12px 15px;
}

.timestamp {
  font-size: 0.75rem;
  color: var(--light-text);
  margin-top: 5px;
  padding: 0 5px;
}

/* AI 消息时间戳靠左显示 */
.ai-message .timestamp {
  text-align: left;
}

/* 用户消息时间戳靠右显示 */
.user-message .timestamp {
  text-align: right;
}

/* 消息操作按钮 */
.message-actions {
  display: flex;
  gap: 5px;
  margin-top: 5px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.ai-message:hover .message-actions,
.user-message:hover .message-actions {
  opacity: 1;
}

.action-btn {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid var(--border-color);
  border-radius: 15px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  height: 24px;
}

.action-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.copy-btn:hover {
  background: #d2e0f3;
  border-color: #d2e0f3;
  transform: scale(1.1);
}

.regenerate-btn:hover {
  background: #9aaedf;
  border-color: #9aaedf;
  transform: scale(1.1);
}

.edit-btn:hover {
  background: #b7caea;
  border-color: #b7caea;
  transform: scale(1.1);
}

/* 用户消息操作按钮位置调整 */
.user-message .message-actions {
  justify-content: flex-end;
}

/* 编辑界面样式 */
.edit-container {
  width: 100%;
  margin-top: 5px;
}

.edit-textarea {
  width: 100%;
  min-height: 40px;
  max-height: 200px;
  padding: 8px 12px;
  border: 2px solid var(--primary-color);
  border-radius: 8px;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.4;
  resize: none;
  outline: none;
  background: white;
  box-sizing: border-box;
}

.edit-textarea:focus {
  border-color: var(--ai-color);
  box-shadow: 0 0 0 2px rgba(172, 76, 240, 0.2);
}

.edit-buttons {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  justify-content: flex-end;
}

.edit-save-btn,
.edit-cancel-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-save-btn {
  background: var(--primary-color);
  color: white;
}

.edit-save-btn:hover {
  background: var(--ai-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.edit-cancel-btn {
  background: #6c757d;
  color: white;
}

.edit-cancel-btn:hover {
  background: #5a6268;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.edit-save-btn:active,
.edit-cancel-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.chat-input {
  display: flex;
  padding: 15px;
  background-color: white;
  border-top: 1px solid var(--border-color);
}

.chat-input textarea {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid var(--border-color);
  border-radius: 25px;
  outline: none;
  font-size: 1rem;
  font-family: inherit;
  resize: none;
  min-height: 44px;
  max-height: 120px;
  overflow-y: auto;
  line-height: 1.4;
  transition: border-color 0.3s;
}

.chat-input textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(238, 67, 175, 0.2);
}

.chat-input button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 12px 25px;
  margin-left: 10px;
  cursor: pointer;
  font-weight: 600;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.chat-input button:hover {
  background-color: var(--secondary-color);
  transform: scale(1.05);
  box-shadow: 0 6px 10px rgba(0,0,0,0.15);
}

.chat-input button:active {
  transform: scale(0.96);
  box-shadow: 0 3px 5px rgba(0,0,0,0.2);
}

.chat-input button:disabled {
  background-color: #b7b6b6;
  color: #3f3f3f;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.chat-input button:disabled:hover {
  background-color: #ccc;
  transform: none;
  box-shadow: none;
}

.footer {
  text-align: center;
  padding: 10px;
  font-size: 0.85rem;
  color: var(--light-text);
  background-color: #f1f3f5;
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-container {
    height: 95vh;
    border-radius: 10px;
    max-width: 100%;
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }

  .session-list {
    flex-direction: row;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 10px;
  }

  .session-item {
    min-width: 150px;
    margin: 0 5px;
  }

  .main-chat {
    flex: 1;
  }

  .message {
    max-width: 90%;
  }

  .main-chat .chat-header h1 {
    font-size: 1.5rem;
  }

  .chat-input button {
    padding: 12px 20px;
  }
}

/* Markdown 样式 */
.message .text h1,
.message .text h2,
.message .text h3,
.message .text h4,
.message .text h5,
.message .text h6 {
  margin: 0.5em 0;
  color: var(--text-color);
  font-weight: 600;
}

.message .text h1 { font-size: 1.5em; }
.message .text h2 { font-size: 1.3em; }
.message .text h3 { font-size: 1.1em; }
.message .text h4 { font-size: 1em; }

.message .text p {
  margin: 0.5em 0;
  line-height: 1.6;
}

.message .text ul,
.message .text ol {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.message .text li {
  margin: 0.2em 0;
  line-height: 1.5;
}

.message .text blockquote {
  border-left: 4px solid var(--ai-color);
  margin: 0.5em 0;
  padding: 0.5em 1em;
  background-color: rgba(172, 76, 240, 0.1);
  border-radius: 0 5px 5px 0;
}

.message .text code {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.message .text pre {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 5px;
  padding: 1em;
  margin: 0.5em 0;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.message .text pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

.message .text strong {
  font-weight: 600;
  color: var(--text-color);
}

.message .text em {
  font-style: italic;
}

.message .text a {
  color: var(--ai-color);
  text-decoration: none;
}

.message .text a:hover {
  text-decoration: underline;
}

.message .text hr {
  border: none;
  border-top: 1px solid #e9ecef;
  margin: 1em 0;
}

.message .text table {
  border-collapse: collapse;
  width: 100%;
  margin: 0.5em 0;
  font-size: 0.9em;
}

.message .text th,
.message .text td {
  border: 1px solid #e9ecef;
  padding: 0.5em;
  text-align: left;
}

.message .text th {
  background-color: rgba(172, 76, 240, 0.1);
  font-weight: 600;
}

/* 确保 Markdown 内容不会破坏布局 */
.message .text * {
  max-width: 100%;
  word-wrap: break-word;
}

.message .text img {
  max-width: 100%;
  height: auto;
  border-radius: 5px;
  margin: 0.5em 0;
}

/* 省略号闪烁 */
.dots span {
  animation: blink 1.5s infinite;
}

.dots span:nth-child(1) {
  animation-delay: 0s;
}

.dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes blink {
  0%, 33% {
    opacity: 1;
  }
  34%, 100% {
    opacity: 0.3;
  }
}

/* Toast 提示样式 */
.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 4px 12px rgba(50, 34, 63, 0.15);
  z-index: 1000;
  font-size: 14px;
  font-weight: 500;
  max-width: 300px;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease;
}

.toast.show {
  opacity: 1;
  transform: translateX(0);
}

.toast-success {
  border-left: 4px solid #7749a5;
  color: #2e1557;
  background-color: #d4dced;
}

.toast-error {
  border-left: 4px solid #dc3545;
  color: #721c24;
  background-color: #f8d7da;
}

.toast-info {
  border-left: 4px solid #17a2b8;
  color: #0c5460;
  background-color: #d1ecf1;
}

/* 配置界面样式 */
.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.config-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  background-color: #f4c742;
  color: #856404;
  margin-left: 8px;
}

.config-status.configured {
  background-color: #8e6acb;
  color: white;
}

.settings-btn {
  width: 45px;
  height: 45px;
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.settings-btn:hover {
  background-color: rgba(77, 44, 93, 0.556);
  transform: scale(1.1);
}

/* 模态框样式 */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
}

.modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background-color: white;
  border-radius: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 90%;
  max-height: 95vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 10px;
  border-bottom: 1px solid var(--border-color);
  height: 60px;
}

.modal-header h2 {
  margin: 0;
  color: var(--text-color);
  font-size: 20px;
}

.close-btn {
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  font-size: 26px;
  cursor: pointer;
  color: var(--light-text);
  
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background-color: rgba(57, 17, 66, 0.318);
  transform: scale(1.1);
}

.modal-body {
  padding: 24px;
}

/* 配置表单样式 */
.config-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
}

.form-group label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 14px;
}

.required {
  color: #dc3545;
}

.form-group input,
.form-group select {
  padding: 12px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
}

.form-help {
  font-size: 12px;
  color: var(--help-text-color);
  margin-top: 4px;
  line-height: 1.4;
}

.toggle-password {
  position: absolute;
  right: 12px;
  top: 36px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
}

.form-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 20px;
}

.btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  flex: 1;
  min-width: 120px;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--secondary-color);
  transform: scale(1.05);
}

.btn-test {
  background-color: #b7caea;
  color: white;
}

.btn-test:hover {
  background-color: #97a7c2;
  transform: scale(1.1);
}

.btn-test:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.btn-test:disabled::after {
  content: "";
  display: inline-block;
  width: 12px;
  height: 12px;
  margin-left: 8px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.btn-reset {
  background-color: #9aaedf;
  color: white;
}

.btn-reset:hover {
  background-color: #8697c2;
  transform: scale(1.1);
}

.config-status-message {
  padding: 16px;
  border-radius: 12px;
  font-size: 15px;
  font-weight: 600;
  margin-top: 10px;
  margin-bottom: 20px;
  display: none;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideInUp 0.6s ease;
  position: relative;
  z-index: 10;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.config-status-message.success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
  border: 2px solid #3bb557;
  display: block;
}

.config-status-message.success::before {
  content: "✅ ";
  font-size: 18px;
}

.config-status-message.error {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
  border: 2px solid #dc3545;
  display: block;
}

.config-status-message.error::before {
  content: "❌ ";
  font-size: 18px;
}

.config-status-message.info {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  color: #0c5460;
  border: 2px solid #17a2b8;
  display: block;
}

.config-status-message.info::before {
  content: "ℹ️ ";
  font-size: 18px;
}

/* 欢迎配置界面样式 */
.welcome-message {
  text-align: center;
  color: var(--text-color);
  line-height: 1.6;
}

.welcome-message ul {
  text-align: left;
  margin: 16px 0;
  padding-left: 20px;
}

.welcome-message li {
  margin: 8px 0;
}